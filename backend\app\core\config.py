import os
from typing import List
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """应用配置"""
    
    # Redis 配置
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # 文件上传配置
    MAX_UPLOAD_SIZE: int = 1073741824  # 1GB
    UPLOAD_DIR: str = "/tmp/uploads"
    CHUNK_DIR: str = "/tmp/chunks"
    
    # 并发控制
    MAX_CONCURRENT_TASKS: int = 3
    MAX_REQUESTS_PER_MINUTE: int = 30
    
    # ElevenLabs API 配置
    ELEVENLABS_API_URL: str = "https://api.elevenlabs.io/v1/speech-to-text"
    ELEVENLABS_TIMEOUT: int = 1800
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "/app/logs/app.log"
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-here"
    CORS_ORIGINS: List[str] = ["http://localhost:3000", "https://yourdomain.com"]
    
    # 应用配置
    APP_NAME: str = "ElevenLabs STT Backend"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 创建全局配置实例
settings = Settings()

# 确保必要的目录存在
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
os.makedirs(settings.CHUNK_DIR, exist_ok=True)
os.makedirs(os.path.dirname(settings.LOG_FILE), exist_ok=True)
