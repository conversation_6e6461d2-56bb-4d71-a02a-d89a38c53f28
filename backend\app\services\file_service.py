import os
import aiofiles
import shutil
from typing import Optional
from fastapi import UploadFile
from app.models.response_models import FileInfo
from app.core.config import settings

class FileService:
    """文件处理服务"""
    
    def __init__(self):
        self.upload_dir = settings.UPLOAD_DIR
        self.chunk_dir = settings.CHUNK_DIR
        
        # 确保目录存在
        os.makedirs(self.upload_dir, exist_ok=True)
        os.makedirs(self.chunk_dir, exist_ok=True)
    
    async def save_upload_file(self, task_id: str, file: UploadFile) -> str:
        """保存上传的文件"""
        # 生成文件路径
        file_extension = os.path.splitext(file.filename)[1] if file.filename else ""
        file_path = os.path.join(self.upload_dir, f"{task_id}{file_extension}")
        
        # 保存文件
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        return file_path
    
    async def get_file_info(self, file_path: str) -> FileInfo:
        """获取文件信息"""
        filename = os.path.basename(file_path)
        size = os.path.getsize(file_path)
        
        # 检测MIME类型
        mime_type = self._detect_mime_type(file_path)
        
        # 尝试获取音频/视频时长（可选实现）
        duration = await self._get_media_duration(file_path)
        
        return FileInfo(
            filename=filename,
            size=size,
            duration=duration,
            mime_type=mime_type
        )
    
    def _detect_mime_type(self, file_path: str) -> str:
        """检测MIME类型"""
        ext = os.path.splitext(file_path)[1].lower()
        mime_map = {
            '.mp3': 'audio/mp3',
            '.wav': 'audio/wav',
            '.flac': 'audio/flac',
            '.m4a': 'audio/m4a',
            '.aac': 'audio/aac',
            '.ogg': 'audio/ogg',
            '.mp4': 'video/mp4',
            '.mov': 'video/mov',
        }
        return mime_map.get(ext, 'application/octet-stream')
    
    async def _get_media_duration(self, file_path: str) -> Optional[float]:
        """获取媒体文件时长（简化实现，实际可使用ffprobe）"""
        # 这里可以集成ffprobe来获取真实的媒体时长
        # 暂时返回None，后续可以扩展
        return None
    
    def cleanup_file(self, file_path: str) -> None:
        """清理文件"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception:
            pass  # 忽略清理错误
    
    async def save_chunk(self, task_id: str, chunk_index: int, chunk_data: bytes) -> str:
        """保存文件块"""
        chunk_filename = f"{task_id}_chunk_{chunk_index}"
        chunk_path = os.path.join(self.chunk_dir, chunk_filename)
        
        async with aiofiles.open(chunk_path, 'wb') as f:
            await f.write(chunk_data)
        
        return chunk_path
    
    async def merge_chunks(self, task_id: str, total_chunks: int, final_filename: str) -> str:
        """合并文件块"""
        final_path = os.path.join(self.upload_dir, f"{task_id}_{final_filename}")
        
        async with aiofiles.open(final_path, 'wb') as final_file:
            for i in range(total_chunks):
                chunk_path = os.path.join(self.chunk_dir, f"{task_id}_chunk_{i}")
                if os.path.exists(chunk_path):
                    async with aiofiles.open(chunk_path, 'rb') as chunk_file:
                        chunk_data = await chunk_file.read()
                        await final_file.write(chunk_data)
                    
                    # 删除临时块文件
                    os.remove(chunk_path)
        
        return final_path
