from fastapi import APIRouter, File, UploadFile, Form, BackgroundTasks, HTTPException, Depends
from fastapi.responses import JSO<PERSON>esponse, FileResponse
from typing import Optional
import uuid
import os
import json
from datetime import datetime

from app.models.request_models import TranscribeRequest, SrtExportOptions
from app.models.response_models import TranscribeResponse, TaskStatusResponse, FileInfo
from app.services.elevenlabs_client import AsyncElevenLabsSTTClient
from app.services.task_service import TaskService
from app.services.file_service import FileService
from app.services.srt_service import SrtService
from app.utils.validators import validate_file_format
from app.core.dependencies import get_task_service, get_file_service

router = APIRouter(prefix="/api/v1/transcribe", tags=["转录"])

@router.post("/upload", response_model=TranscribeResponse)
async def upload_audio(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(..., description="音频或视频文件"),
    language_code: Optional[str] = Form(None, description="语言代码 (如: zh, en, ja)"),
    tag_audio_events: bool = Form(True, description="是否标记音频事件"),
    num_speakers: Optional[int] = Form(None, ge=1, le=32, description="最大说话人数量"),
    timestamps_granularity: str = Form("word", description="时间戳粒度: none/word/character"),
    additional_formats: Optional[str] = Form(None, description="额外格式配置 (JSON字符串)"),
    task_service: TaskService = Depends(get_task_service),
    file_service: FileService = Depends(get_file_service)
):
    """
    上传音频文件进行转录

    支持的文件格式: MP3, WAV, FLAC, M4A, AAC, OGG, MP4, MOV
    最大文件大小: 1GB
    """

    # 生成任务ID
    task_id = str(uuid.uuid4())

    try:
        # 验证文件格式
        validate_file_format(file.filename)

        # 解析额外格式配置
        parsed_additional_formats = None
        if additional_formats:
            try:
                formats_data = json.loads(additional_formats)
                parsed_additional_formats = [SrtExportOptions(**fmt) for fmt in formats_data]
            except (json.JSONDecodeError, ValueError) as e:
                raise HTTPException(status_code=400, detail=f"额外格式配置解析错误: {e}")

        # 构建请求参数
        request_params = TranscribeRequest(
            language_code=language_code,
            tag_audio_events=tag_audio_events,
            num_speakers=num_speakers,
            timestamps_granularity=timestamps_granularity,
            additional_formats=parsed_additional_formats
        )

        # 保存上传文件
        file_path = await file_service.save_upload_file(task_id, file)

        # 获取文件信息
        file_info = await file_service.get_file_info(file_path)

        # 初始化任务状态
        await task_service.create_task(task_id, file_info, request_params)

        # 启动后台转录任务
        background_tasks.add_task(
            process_transcription_task,
            task_id=task_id,
            file_path=file_path,
            request_params=request_params,
            task_service=task_service
        )

        return TranscribeResponse(
            task_id=task_id,
            status="processing",
            message="文件上传成功，开始处理",
            file_info=file_info
        )

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/status/{task_id}", response_model=TaskStatusResponse)
async def get_transcription_status(
    task_id: str,
    task_service: TaskService = Depends(get_task_service)
):
    """查询转录任务状态"""

    task_status = await task_service.get_task_status(task_id)

    if not task_status:
        raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")

    return task_status

@router.get("/download/{task_id}/{filename}")
async def download_srt_file(
    task_id: str,
    filename: str,
    task_service: TaskService = Depends(get_task_service)
):
    """下载SRT字幕文件"""

    # 验证任务存在且已完成
    task_status = await task_service.get_task_status(task_id)
    if not task_status:
        raise HTTPException(status_code=404, detail="任务不存在")

    if task_status.status != "completed":
        raise HTTPException(status_code=400, detail="任务尚未完成")

    # 获取文件路径
    srt_service = SrtService()
    file_path = await srt_service.get_srt_file(task_id, filename)

    if not file_path or not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="文件不存在")

    # 返回文件
    return FileResponse(
        path=file_path,
        filename=filename,
        media_type="text/srt"
    )

async def process_transcription_task(
    task_id: str,
    file_path: str,
    request_params: TranscribeRequest,
    task_service: TaskService
):
    """处理转录任务"""

    try:
        # 更新任务状态
        await task_service.update_task_progress(task_id, 10, "开始调用 ElevenLabs API...")

        # 执行转录
        async with AsyncElevenLabsSTTClient() as client:
            api_response = await client.transcribe_file(file_path, request_params)

        # 更新进度
        await task_service.update_task_progress(task_id, 80, "处理转录结果...")

        # 处理SRT文件
        srt_service = SrtService()
        processed_result = await srt_service.process_additional_formats(task_id, api_response)

        # 更新进度
        await task_service.update_task_progress(task_id, 95, "生成字幕文件...")

        # 计算处理时间
        processing_time = await task_service.calculate_processing_time(task_id)

        # 更新完成状态
        await task_service.complete_task(task_id, processed_result, processing_time)

    except Exception as e:
        # 更新错误状态
        await task_service.fail_task(task_id, str(e))

    finally:
        # 清理临时文件
        try:
            os.remove(file_path)
        except:
            pass
