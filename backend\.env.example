# Redis 配置
REDIS_URL=redis://localhost:6379/0

# 文件上传配置
MAX_UPLOAD_SIZE=1073741824  # 1GB
UPLOAD_DIR=/tmp/uploads
CHUNK_DIR=/tmp/chunks

# 并发控制
MAX_CONCURRENT_TASKS=3
MAX_REQUESTS_PER_MINUTE=30

# ElevenLabs API 配置
ELEVENLABS_API_URL=https://api.elevenlabs.io/v1/speech-to-text
ELEVENLABS_TIMEOUT=1800

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/app/logs/app.log

# 安全配置
SECRET_KEY=your-secret-key-here
CORS_ORIGINS=["http://localhost:3000", "https://yourdomain.com"]

# 应用配置
APP_NAME=ElevenLabs STT Backend
APP_VERSION=1.0.0
DEBUG=false
