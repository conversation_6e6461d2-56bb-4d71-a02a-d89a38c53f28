from pydantic import BaseModel, Field, validator
from typing import Optional, List, Literal
from enum import Enum

class TimestampsGranularity(str, Enum):
    NONE = "none"
    WORD = "word"
    CHARACTER = "character"

class SrtExportOptions(BaseModel):
    format: Literal["srt"] = "srt"
    max_characters_per_line: Optional[int] = Field(default=50, ge=1, le=200)
    include_speakers: bool = Field(default=False)
    include_timestamps: bool = Field(default=True)
    segment_on_silence_longer_than_s: Optional[float] = Field(default=0.6, ge=0.1, le=10.0)
    max_segment_duration_s: Optional[float] = Field(default=5.0, ge=1.0, le=30.0)
    max_segment_chars: Optional[int] = Field(default=90, ge=10, le=500)

class TranscribeRequest(BaseModel):
    language_code: Optional[str] = Field(default=None, max_length=10)
    tag_audio_events: bool = Field(default=True)
    num_speakers: Optional[int] = Field(default=None, ge=1, le=32)
    timestamps_granularity: TimestampsGranularity = Field(default=TimestampsGranularity.WORD)
    additional_formats: Optional[List[SrtExportOptions]] = Field(default=None)
    
    @validator('language_code')
    def validate_language_code(cls, v):
        if v is not None:
            # 验证语言代码格式
            valid_codes = ['zh', 'en', 'ja', 'ko', 'es', 'fr', 'de', 'ru', 'it', 'pt']
            if v not in valid_codes:
                raise ValueError(f'不支持的语言代码: {v}')
        return v

class ChunkUploadRequest(BaseModel):
    task_id: str = Field(..., min_length=1)
    chunk_index: int = Field(..., ge=0)
    total_chunks: int = Field(..., ge=1)
