from fastapi import Depends
from app.services.task_service import TaskService
from app.services.file_service import FileService
from app.services.srt_service import SrtService

# 服务依赖注入
def get_task_service() -> TaskService:
    """获取任务服务实例"""
    return TaskService()

def get_file_service() -> FileService:
    """获取文件服务实例"""
    return FileService()

def get_srt_service() -> SrtService:
    """获取SRT服务实例"""
    return SrtService()

# 可以在这里添加其他依赖注入，比如数据库连接、认证等
